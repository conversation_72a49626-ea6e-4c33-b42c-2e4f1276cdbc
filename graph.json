{"success": true, "error": null, "results": {"printers": [{"elements": [{"type": "file", "name": {"filename": "all_contracts.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_224_Ownable {\nlabel = \"Ownable\"\n\"224_transferOwnership\" [label=\"transferOwnership\"]\n\"224_renounceOwnership\" [label=\"renounceOwnership\"]\n\"224_constructor\" [label=\"constructor\"]\n\"224_owner\" [label=\"owner\"]\n\"224_constructor\" -> \"224__msgSender\"\n\"224_transferOwnership\" -> \"224_onlyOwner\"\n\"224_renounceOwnership\" -> \"224_onlyOwner\"\n}subgraph cluster_293_IERC165 {\nlabel = \"IERC165\"\n\"293_supportsInterface\" [label=\"supportsInterface\"]\n}subgraph cluster_2662_EnumerableMap {\nlabel = \"EnumerableMap\"\n\"2662__contains\" [label=\"_contains\"]\n\"2662_length\" [label=\"length\"]\n\"2662_tryGet\" [label=\"tryGet\"]\n\"2662_contains\" [label=\"contains\"]\n\"2662__at\" [label=\"_at\"]\n\"2662_get\" [label=\"get\"]\n\"2662__length\" [label=\"_length\"]\n\"2662_remove\" [label=\"remove\"]\n\"2662__tryGet\" [label=\"_tryGet\"]\n\"2662_at\" [label=\"at\"]\n\"2662__set\" [label=\"_set\"]\n\"2662__remove\" [label=\"_remove\"]\n\"2662_set\" [label=\"set\"]\n\"2662__get\" [label=\"_get\"]\n\"2662_get\" -> \"2662__get\"\n\"2662_remove\" -> \"2662__remove\"\n\"2662_tryGet\" -> \"2662__tryGet\"\n\"2662_set\" -> \"2662__set\"\n\"2662_contains\" -> \"2662__contains\"\n\"2662_at\" -> \"2662__at\"\n\"2662_length\" -> \"2662__length\"\n}subgraph cluster_1765_IERC721Metadata {\nlabel = \"IERC721Metadata\"\n\"1765_name\" [label=\"name\"]\n\"1765_tokenURI\" [label=\"tokenURI\"]\n\"1765_symbol\" [label=\"symbol\"]\n}subgraph cluster_648_SafeMath {\nlabel = \"SafeMath\"\n\"648_tryMod\" [label=\"tryMod\"]\n\"648_mul\" [label=\"mul\"]\n\"648_mod\" [label=\"mod\"]\n\"648_tryDiv\" [label=\"tryDiv\"]\n\"648_tryMul\" [label=\"tryMul\"]\n\"648_div\" [label=\"div\"]\n\"648_trySub\" [label=\"trySub\"]\n\"648_add\" [label=\"add\"]\n\"648_tryAdd\" [label=\"tryAdd\"]\n\"648_sub\" [label=\"sub\"]\n}subgraph cluster_3241_Strings {\nlabel = \"Strings\"\n\"3241_toString\" [label=\"toString\"]\n}subgraph cluster_1738_IERC721Enumerable {\nlabel = \"IERC721Enumerable\"\n\"1738_tokenOfOwnerByIndex\" [label=\"tokenOfOwnerByIndex\"]\n\"1738_totalSupply\" [label=\"totalSupply\"]\n\"1738_tokenByIndex\" [label=\"tokenByIndex\"]\n}subgraph cluster_1707_IERC721 {\nlabel = \"IERC721\"\n\"1707_getApproved\" [label=\"getApproved\"]\n\"1707_setApprovalForAll\" [label=\"setApprovalForAll\"]\n\"1707_approve\" [label=\"approve\"]\n\"1707_isApprovedForAll\" [label=\"isApprovedForAll\"]\n\"1707_safeTransferFrom\" [label=\"safeTransferFrom\"]\n\"1707_ownerOf\" [label=\"ownerOf\"]\n\"1707_transferFrom\" [label=\"transferFrom\"]\n\"1707_balanceOf\" [label=\"balanceOf\"]\n}subgraph cluster_1783_IERC721Receiver {\nlabel = \"IERC721Receiver\"\n\"1783_onERC721Received\" [label=\"onERC721Received\"]\n}subgraph cluster_3895_PuppyRaffle {\nlabel = \"PuppyRaffle\"\n\"3895_changeFeeAddress\" [label=\"changeFeeAddress\"]\n\"3895_getActivePlayerIndex\" [label=\"getActivePlayerIndex\"]\n\"3895__isActivePlayer\" [label=\"_isActivePlayer\"]\n\"3895_constructor\" [label=\"constructor\"]\n\"3895__baseURI\" [label=\"_baseURI\"]\n\"3895_withdrawFees\" [label=\"withdrawFees\"]\n\"3895_enterRaffle\" [label=\"enterRaffle\"]\n\"3895_selectWinner\" [label=\"selectWinner\"]\n\"3895_tokenURI\" [label=\"tokenURI\"]\n\"3895_refund\" [label=\"refund\"]\n\"3895_changeFeeAddress\" -> \"3895_onlyOwner\"\n\"3895_tokenURI\" -> \"3895_name\"\n\"3895_tokenURI\" -> \"3895__exists\"\n\"3895_selectWinner\" -> \"3895_totalSupply\"\n\"3895_constructor\" -> \"3895_constructor\"\n\"3895_tokenURI\" -> \"3895__baseURI\"\n\"3895_selectWinner\" -> \"3895__safeMint\"\n}subgraph cluster_3154_EnumerableSet {\nlabel = \"EnumerableSet\"\n\"3154_at\" [label=\"at\"]\n\"3154__remove\" [label=\"_remove\"]\n\"3154_add\" [label=\"add\"]\n\"3154__at\" [label=\"_at\"]\n\"3154__add\" [label=\"_add\"]\n\"3154__length\" [label=\"_length\"]\n\"3154_length\" [label=\"length\"]\n\"3154_remove\" [label=\"remove\"]\n\"3154_contains\" [label=\"contains\"]\n\"3154__contains\" [label=\"_contains\"]\n\"3154_remove\" -> \"3154__remove\"\n\"3154_at\" -> \"3154__at\"\n\"3154_add\" -> \"3154__add\"\n\"3154__add\" -> \"3154__contains\"\n\"3154_length\" -> \"3154__length\"\n\"3154_contains\" -> \"3154__contains\"\n}subgraph cluster_114_Base64 {\nlabel = \"Base64\"\n\"114_decode\" [label=\"decode\"]\n\"114_encode\" [label=\"encode\"]\n}subgraph cluster_2102_Context {\nlabel = \"Context\"\n\"2102__msgSender\" [label=\"_msgSender\"]\n\"2102__msgData\" [label=\"_msgData\"]\n}subgraph cluster_1591_ERC721 {\nlabel = \"ERC721\"\n\"1591__exists\" [label=\"_exists\"]\n\"1591_tokenByIndex\" [label=\"tokenByIndex\"]\n\"1591__transfer\" [label=\"_transfer\"]\n\"1591__approve\" [label=\"_approve\"]\n\"1591_baseURI\" [label=\"baseURI\"]\n\"1591__isApprovedOrOwner\" [label=\"_isApprovedOrOwner\"]\n\"1591__checkOnERC721Received\" [label=\"_checkOnERC721Received\"]\n\"1591_ownerOf\" [label=\"ownerOf\"]\n\"1591__burn\" [label=\"_burn\"]\n\"1591__beforeTokenTransfer\" [label=\"_beforeTokenTransfer\"]\n\"1591__setTokenURI\" [label=\"_setTokenURI\"]\n\"1591__safeMint\" [label=\"_safeMint\"]\n\"1591_name\" [label=\"name\"]\n\"1591_tokenURI\" [label=\"tokenURI\"]\n\"1591__setBaseURI\" [label=\"_setBaseURI\"]\n\"1591_tokenOfOwnerByIndex\" [label=\"tokenOfOwnerByIndex\"]\n\"1591_constructor\" [label=\"constructor\"]\n\"1591_balanceOf\" [label=\"balanceOf\"]\n\"1591_isApprovedForAll\" [label=\"isApprovedForAll\"]\n\"1591_getApproved\" [label=\"getApproved\"]\n\"1591_approve\" [label=\"approve\"]\n\"1591_safeTransferFrom\" [label=\"safeTransferFrom\"]\n\"1591_transferFrom\" [label=\"transferFrom\"]\n\"1591_symbol\" [label=\"symbol\"]\n\"1591_totalSupply\" [label=\"totalSupply\"]\n\"1591__mint\" [label=\"_mint\"]\n\"1591__safeTransfer\" [label=\"_safeTransfer\"]\n\"1591_setApprovalForAll\" [label=\"setApprovalForAll\"]\n\"1591__isApprovedOrOwner\" -> \"1591__exists\"\n\"1591_transferFrom\" -> \"1591__isApprovedOrOwner\"\n\"1591__approve\" -> \"1591_ownerOf\"\n\"1591__safeTransfer\" -> \"1591__checkOnERC721Received\"\n\"1591__safeMint\" -> \"1591__mint\"\n\"1591__isApprovedOrOwner\" -> \"1591_getApproved\"\n\"1591_safeTransferFrom\" -> \"1591__msgSender\"\n\"1591__transfer\" -> \"1591_ownerOf\"\n\"1591__checkOnERC721Received\" -> \"1591__msgSender\"\n\"1591_approve\" -> \"1591_ownerOf\"\n\"1591_transferFrom\" -> \"1591__msgSender\"\n\"1591_safeTransferFrom\" -> \"1591_safeTransferFrom\"\n\"1591__safeMint\" -> \"1591__checkOnERC721Received\"\n\"1591_constructor\" -> \"1591__registerInterface\"\n\"1591__transfer\" -> \"1591__approve\"\n\"1591_getApproved\" -> \"1591__exists\"\n\"1591__burn\" -> \"1591__approve\"\n\"1591_safeTransferFrom\" -> \"1591__safeTransfer\"\n\"1591__setTokenURI\" -> \"1591__exists\"\n\"1591_tokenURI\" -> \"1591_baseURI\"\n\"1591_approve\" -> \"1591__msgSender\"\n\"1591_setApprovalForAll\" -> \"1591__msgSender\"\n\"1591__isApprovedOrOwner\" -> \"1591_ownerOf\"\n\"1591_approve\" -> \"1591__approve\"\n\"1591__mint\" -> \"1591__beforeTokenTransfer\"\n\"1591__burn\" -> \"1591__beforeTokenTransfer\"\n\"1591__safeMint\" -> \"1591__safeMint\"\n\"1591__transfer\" -> \"1591__beforeTokenTransfer\"\n\"1591__mint\" -> \"1591__exists\"\n\"1591_safeTransferFrom\" -> \"1591__isApprovedOrOwner\"\n\"1591__burn\" -> \"1591_ownerOf\"\n\"1591_tokenURI\" -> \"1591__exists\"\n\"1591_approve\" -> \"1591_isApprovedForAll\"\n\"1591_transferFrom\" -> \"1591__transfer\"\n\"1591__isApprovedOrOwner\" -> \"1591_isApprovedForAll\"\n\"1591__safeTransfer\" -> \"1591__transfer\"\n}subgraph cluster_281_ERC165 {\nlabel = \"ERC165\"\n\"281_supportsInterface\" [label=\"supportsInterface\"]\n\"281_constructor\" [label=\"constructor\"]\n\"281__registerInterface\" [label=\"_registerInterface\"]\n\"281_constructor\" -> \"281__registerInterface\"\n}subgraph cluster_2079_Address {\nlabel = \"Address\"\n\"2079_functionDelegateCall\" [label=\"functionDelegateCall\"]\n\"2079_functionCallWithValue\" [label=\"functionCallWithValue\"]\n\"2079_sendValue\" [label=\"sendValue\"]\n\"2079_functionCall\" [label=\"functionCall\"]\n\"2079__verifyCallResult\" [label=\"_verifyCallResult\"]\n\"2079_isContract\" [label=\"isContract\"]\n\"2079_functionStaticCall\" [label=\"functionStaticCall\"]\n\"2079_functionDelegateCall\" -> \"2079__verifyCallResult\"\n\"2079_functionCall\" -> \"2079_functionCallWithValue\"\n\"2079_functionStaticCall\" -> \"2079_isContract\"\n\"2079_functionDelegateCall\" -> \"2079_functionDelegateCall\"\n\"2079_functionCall\" -> \"2079_functionCall\"\n\"2079_functionCallWithValue\" -> \"2079__verifyCallResult\"\n\"2079_functionStaticCall\" -> \"2079_functionStaticCall\"\n\"2079_functionDelegateCall\" -> \"2079_isContract\"\n\"2079_functionStaticCall\" -> \"2079__verifyCallResult\"\n\"2079_functionCallWithValue\" -> \"2079_functionCallWithValue\"\n\"2079_functionCallWithValue\" -> \"2079_isContract\"\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n\"mstore8(uint256,uint256)\" \n\"abi.decode()\" \n\"revert(string)\" \n\"mstore(uint256,uint256)\" \n\"mload(uint256)\" \n\"balance(address)\" \n\"revert(uint256,uint256)\" \n\"abi.encodeWithSelector()\" \n\"abi.encodePacked()\" \n\"require(bool,string)\" \n\"keccak256(bytes)\" \n\"2079_sendValue\" -> \"balance(address)\"\n\"2662__get\" -> \"require(bool,string)\"\n\"648_mod\" -> \"require(bool,string)\"\n\"1591__checkOnERC721Received\" -> \"abi.encodeWithSelector()\"\n\"3895_withdrawFees\" -> \"require(bool,string)\"\n\"1591_tokenURI\" -> \"abi.encodePacked()\"\n\"1591__mint\" -> \"require(bool,string)\"\n\"2079_functionCallWithValue\" -> \"require(bool,string)\"\n\"114_decode\" -> \"require(bool,string)\"\n\"2079_functionDelegateCall\" -> \"require(bool,string)\"\n\"1591_transferFrom\" -> \"require(bool,string)\"\n\"648_sub\" -> \"require(bool,string)\"\n\"3154__at\" -> \"require(bool,string)\"\n\"648_mul\" -> \"require(bool,string)\"\n\"2662__at\" -> \"require(bool,string)\"\n\"114_encode\" -> \"mstore(uint256,uint256)\"\n\"2079_functionStaticCall\" -> \"require(bool,string)\"\n\"3895_refund\" -> \"require(bool,string)\"\n\"114_encode\" -> \"mstore8(uint256,uint256)\"\n\"281__registerInterface\" -> \"require(bool,string)\"\n\"648_div\" -> \"require(bool,string)\"\n\"2079__verifyCallResult\" -> \"mload(uint256)\"\n\"1591_getApproved\" -> \"require(bool,string)\"\n\"1591_safeTransferFrom\" -> \"require(bool,string)\"\n\"1591__safeTransfer\" -> \"require(bool,string)\"\n\"3895_withdrawFees\" -> \"balance(address)\"\n\"224_transferOwnership\" -> \"require(bool,string)\"\n\"3895_selectWinner\" -> \"keccak256(bytes)\"\n\"3895_selectWinner\" -> \"abi.encodePacked()\"\n\"3895_selectWinner\" -> \"require(bool,string)\"\n\"1591_tokenURI\" -> \"require(bool,string)\"\n\"1591__transfer\" -> \"require(bool,string)\"\n\"1591__checkOnERC721Received\" -> \"abi.decode()\"\n\"1591_approve\" -> \"require(bool,string)\"\n\"1591_setApprovalForAll\" -> \"require(bool,string)\"\n\"114_decode\" -> \"mload(uint256)\"\n\"2079_functionCallWithValue\" -> \"balance(address)\"\n\"3895_enterRaffle\" -> \"require(bool,string)\"\n\"114_decode\" -> \"mstore(uint256,uint256)\"\n\"648_add\" -> \"require(bool,string)\"\n\"2079_sendValue\" -> \"require(bool,string)\"\n\"3895_tokenURI\" -> \"require(bool,string)\"\n\"114_encode\" -> \"mload(uint256)\"\n\"1591_balanceOf\" -> \"require(bool,string)\"\n\"3895_tokenURI\" -> \"abi.encodePacked()\"\n\"2079__verifyCallResult\" -> \"revert(string)\"\n\"1591__isApprovedOrOwner\" -> \"require(bool,string)\"\n\"1591__setTokenURI\" -> \"require(bool,string)\"\n\"1591__safeMint\" -> \"require(bool,string)\"\n\"2079__verifyCallResult\" -> \"revert(uint256,uint256)\"\n}\"1591_balanceOf\" -> \"3154_length\"\n\"1591_totalSupply\" -> \"2662_length\"\n\"1591__burn\" -> \"2662_remove\"\n\"1591__transfer\" -> \"3154_add\"\n\"1591_tokenByIndex\" -> \"2662_at\"\n\"1591__exists\" -> \"2662_contains\"\n\"1591__transfer\" -> \"2662_set\"\n\"1591_tokenOfOwnerByIndex\" -> \"3154_at\"\n\"1591__transfer\" -> \"3154_remove\"\n\"1591__checkOnERC721Received\" -> \"2079_isContract\"\n\"3895_tokenURI\" -> \"114_encode\"\n\"1591__burn\" -> \"3154_remove\"\n\"1591_ownerOf\" -> \"2662_get\"\n\"1591__checkOnERC721Received\" -> \"2079_functionCall\"\n\"1591_tokenURI\" -> \"3241_toString\"\n\"3895_refund\" -> \"2079_sendValue\"\n\"1591__mint\" -> \"2662_set\"\n\"1591__mint\" -> \"3154_add\"\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "Base64.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_114_Base64 {\nlabel = \"Base64\"\n\"114_decode\" [label=\"decode\"]\n\"114_encode\" [label=\"encode\"]\n\"114_slitherConstructorConstantVariables\" [label=\"slitherConstructorConstantVariables\"]\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n\"mload(uint256)\" \n\"mstore8(uint256,uint256)\" \n\"require(bool,string)\" \n\"mstore(uint256,uint256)\" \n\"114_decode\" -> \"mstore(uint256,uint256)\"\n\"114_encode\" -> \"mload(uint256)\"\n\"114_decode\" -> \"require(bool,string)\"\n\"114_encode\" -> \"mstore8(uint256,uint256)\"\n\"114_decode\" -> \"mload(uint256)\"\n\"114_encode\" -> \"mstore(uint256,uint256)\"\n}\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "SafeMath.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_648_SafeMath {\nlabel = \"SafeMath\"\n\"648_tryMod\" [label=\"tryMod\"]\n\"648_mul\" [label=\"mul\"]\n\"648_mod\" [label=\"mod\"]\n\"648_tryDiv\" [label=\"tryDiv\"]\n\"648_tryMul\" [label=\"tryMul\"]\n\"648_div\" [label=\"div\"]\n\"648_trySub\" [label=\"trySub\"]\n\"648_add\" [label=\"add\"]\n\"648_tryAdd\" [label=\"tryAdd\"]\n\"648_sub\" [label=\"sub\"]\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n\"require(bool,string)\" \n\"648_add\" -> \"require(bool,string)\"\n\"648_mod\" -> \"require(bool,string)\"\n\"648_mul\" -> \"require(bool,string)\"\n\"648_sub\" -> \"require(bool,string)\"\n\"648_div\" -> \"require(bool,string)\"\n}\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "IERC721Receiver.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_1783_IERC721Receiver {\nlabel = \"IERC721Receiver\"\n\"1783_onERC721Received\" [label=\"onERC721Received\"]\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n}\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "Address.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_2079_Address {\nlabel = \"Address\"\n\"2079_functionDelegateCall\" [label=\"functionDelegateCall\"]\n\"2079_functionCallWithValue\" [label=\"functionCallWithValue\"]\n\"2079_sendValue\" [label=\"sendValue\"]\n\"2079_functionCall\" [label=\"functionCall\"]\n\"2079__verifyCallResult\" [label=\"_verifyCallResult\"]\n\"2079_isContract\" [label=\"isContract\"]\n\"2079_functionStaticCall\" [label=\"functionStaticCall\"]\n\"2079_functionDelegateCall\" -> \"2079__verifyCallResult\"\n\"2079_functionCall\" -> \"2079_functionCallWithValue\"\n\"2079_functionStaticCall\" -> \"2079_isContract\"\n\"2079_functionDelegateCall\" -> \"2079_functionDelegateCall\"\n\"2079_functionCall\" -> \"2079_functionCall\"\n\"2079_functionCallWithValue\" -> \"2079__verifyCallResult\"\n\"2079_functionStaticCall\" -> \"2079_functionStaticCall\"\n\"2079_functionDelegateCall\" -> \"2079_isContract\"\n\"2079_functionStaticCall\" -> \"2079__verifyCallResult\"\n\"2079_functionCallWithValue\" -> \"2079_functionCallWithValue\"\n\"2079_functionCallWithValue\" -> \"2079_isContract\"\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n\"revert(string)\" \n\"mload(uint256)\" \n\"balance(address)\" \n\"revert(uint256,uint256)\" \n\"require(bool,string)\" \n\"2079_sendValue\" -> \"balance(address)\"\n\"2079_functionCallWithValue\" -> \"balance(address)\"\n\"2079_functionStaticCall\" -> \"require(bool,string)\"\n\"2079_sendValue\" -> \"require(bool,string)\"\n\"2079_functionCallWithValue\" -> \"require(bool,string)\"\n\"2079_functionDelegateCall\" -> \"require(bool,string)\"\n\"2079__verifyCallResult\" -> \"mload(uint256)\"\n\"2079__verifyCallResult\" -> \"revert(string)\"\n\"2079__verifyCallResult\" -> \"revert(uint256,uint256)\"\n}\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "EnumerableMap.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_2662_EnumerableMap {\nlabel = \"EnumerableMap\"\n\"2662__contains\" [label=\"_contains\"]\n\"2662_length\" [label=\"length\"]\n\"2662_tryGet\" [label=\"tryGet\"]\n\"2662_contains\" [label=\"contains\"]\n\"2662__at\" [label=\"_at\"]\n\"2662_get\" [label=\"get\"]\n\"2662__length\" [label=\"_length\"]\n\"2662_remove\" [label=\"remove\"]\n\"2662__tryGet\" [label=\"_tryGet\"]\n\"2662_at\" [label=\"at\"]\n\"2662__set\" [label=\"_set\"]\n\"2662__remove\" [label=\"_remove\"]\n\"2662_set\" [label=\"set\"]\n\"2662__get\" [label=\"_get\"]\n\"2662_remove\" -> \"2662__remove\"\n\"2662_get\" -> \"2662__get\"\n\"2662_set\" -> \"2662__set\"\n\"2662_tryGet\" -> \"2662__tryGet\"\n\"2662_contains\" -> \"2662__contains\"\n\"2662_at\" -> \"2662__at\"\n\"2662_length\" -> \"2662__length\"\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n\"require(bool,string)\" \n\"2662__at\" -> \"require(bool,string)\"\n\"2662__get\" -> \"require(bool,string)\"\n}\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "EnumerableSet.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_3154_EnumerableSet {\nlabel = \"EnumerableSet\"\n\"3154_at\" [label=\"at\"]\n\"3154__remove\" [label=\"_remove\"]\n\"3154_add\" [label=\"add\"]\n\"3154__at\" [label=\"_at\"]\n\"3154__add\" [label=\"_add\"]\n\"3154__length\" [label=\"_length\"]\n\"3154_length\" [label=\"length\"]\n\"3154_remove\" [label=\"remove\"]\n\"3154_contains\" [label=\"contains\"]\n\"3154__contains\" [label=\"_contains\"]\n\"3154_remove\" -> \"3154__remove\"\n\"3154_at\" -> \"3154__at\"\n\"3154_add\" -> \"3154__add\"\n\"3154__add\" -> \"3154__contains\"\n\"3154_length\" -> \"3154__length\"\n\"3154_contains\" -> \"3154__contains\"\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n\"require(bool,string)\" \n\"3154__at\" -> \"require(bool,string)\"\n}\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "Strings.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_3241_Strings {\nlabel = \"Strings\"\n\"3241_toString\" [label=\"toString\"]\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n}\n}"}, "source_mapping": {}}, {"type": "file", "name": {"filename": "PuppyRaffle.call-graph.dot", "content": "strict digraph {\nrankdir=\"LR\"\nnode [shape=box]\nsubgraph cluster_224_Ownable {\nlabel = \"Ownable\"\n\"224_transferOwnership\" [label=\"transferOwnership\"]\n\"224_renounceOwnership\" [label=\"renounceOwnership\"]\n\"224_constructor\" [label=\"constructor\"]\n\"224_owner\" [label=\"owner\"]\n\"224_constructor\" -> \"224__msgSender\"\n\"224_transferOwnership\" -> \"224_onlyOwner\"\n\"224_renounceOwnership\" -> \"224_onlyOwner\"\n}subgraph cluster_293_IERC165 {\nlabel = \"IERC165\"\n\"293_supportsInterface\" [label=\"supportsInterface\"]\n}subgraph cluster_1765_IERC721Metadata {\nlabel = \"IERC721Metadata\"\n\"1765_name\" [label=\"name\"]\n\"1765_tokenURI\" [label=\"tokenURI\"]\n\"1765_symbol\" [label=\"symbol\"]\n}subgraph cluster_1738_IERC721Enumerable {\nlabel = \"IERC721Enumerable\"\n\"1738_tokenOfOwnerByIndex\" [label=\"tokenOfOwnerByIndex\"]\n\"1738_totalSupply\" [label=\"totalSupply\"]\n\"1738_tokenByIndex\" [label=\"tokenByIndex\"]\n}subgraph cluster_1707_IERC721 {\nlabel = \"IERC721\"\n\"1707_getApproved\" [label=\"getApproved\"]\n\"1707_setApprovalForAll\" [label=\"setApprovalForAll\"]\n\"1707_approve\" [label=\"approve\"]\n\"1707_isApprovedForAll\" [label=\"isApprovedForAll\"]\n\"1707_safeTransferFrom\" [label=\"safeTransferFrom\"]\n\"1707_ownerOf\" [label=\"ownerOf\"]\n\"1707_transferFrom\" [label=\"transferFrom\"]\n\"1707_balanceOf\" [label=\"balanceOf\"]\n}subgraph cluster_3895_PuppyRaffle {\nlabel = \"PuppyRaffle\"\n\"3895_slitherConstructorVariables\" [label=\"slitherConstructorVariables\"]\n\"3895_changeFeeAddress\" [label=\"changeFeeAddress\"]\n\"3895_getActivePlayerIndex\" [label=\"getActivePlayerIndex\"]\n\"3895__isActivePlayer\" [label=\"_isActivePlayer\"]\n\"3895_constructor\" [label=\"constructor\"]\n\"3895__baseURI\" [label=\"_baseURI\"]\n\"3895_slitherConstructorConstantVariables\" [label=\"slitherConstructorConstantVariables\"]\n\"3895_withdrawFees\" [label=\"withdrawFees\"]\n\"3895_enterRaffle\" [label=\"enterRaffle\"]\n\"3895_selectWinner\" [label=\"selectWinner\"]\n\"3895_tokenURI\" [label=\"tokenURI\"]\n\"3895_refund\" [label=\"refund\"]\n\"3895_changeFeeAddress\" -> \"3895_onlyOwner\"\n\"3895_tokenURI\" -> \"3895_name\"\n\"3895_tokenURI\" -> \"3895__exists\"\n\"3895_selectWinner\" -> \"3895_totalSupply\"\n\"3895_constructor\" -> \"3895_constructor\"\n\"3895_tokenURI\" -> \"3895__baseURI\"\n\"3895_selectWinner\" -> \"3895__safeMint\"\n}subgraph cluster_2102_Context {\nlabel = \"Context\"\n\"2102__msgSender\" [label=\"_msgSender\"]\n\"2102__msgData\" [label=\"_msgData\"]\n}subgraph cluster_1591_ERC721 {\nlabel = \"ERC721\"\n\"1591__exists\" [label=\"_exists\"]\n\"1591_tokenByIndex\" [label=\"tokenByIndex\"]\n\"1591_baseURI\" [label=\"baseURI\"]\n\"1591__isApprovedOrOwner\" [label=\"_isApprovedOrOwner\"]\n\"1591__transfer\" [label=\"_transfer\"]\n\"1591__approve\" [label=\"_approve\"]\n\"1591__checkOnERC721Received\" [label=\"_checkOnERC721Received\"]\n\"1591_ownerOf\" [label=\"ownerOf\"]\n\"1591__burn\" [label=\"_burn\"]\n\"1591__beforeTokenTransfer\" [label=\"_beforeTokenTransfer\"]\n\"1591__setTokenURI\" [label=\"_setTokenURI\"]\n\"1591__safeMint\" [label=\"_safeMint\"]\n\"1591_name\" [label=\"name\"]\n\"1591_tokenURI\" [label=\"tokenURI\"]\n\"1591__setBaseURI\" [label=\"_setBaseURI\"]\n\"1591_tokenOfOwnerByIndex\" [label=\"tokenOfOwnerByIndex\"]\n\"1591_constructor\" [label=\"constructor\"]\n\"1591_balanceOf\" [label=\"balanceOf\"]\n\"1591_isApprovedForAll\" [label=\"isApprovedForAll\"]\n\"1591_getApproved\" [label=\"getApproved\"]\n\"1591_approve\" [label=\"approve\"]\n\"1591_safeTransferFrom\" [label=\"safeTransferFrom\"]\n\"1591_transferFrom\" [label=\"transferFrom\"]\n\"1591_symbol\" [label=\"symbol\"]\n\"1591_totalSupply\" [label=\"totalSupply\"]\n\"1591__mint\" [label=\"_mint\"]\n\"1591__safeTransfer\" [label=\"_safeTransfer\"]\n\"1591_setApprovalForAll\" [label=\"setApprovalForAll\"]\n\"1591__isApprovedOrOwner\" -> \"1591__exists\"\n\"1591_transferFrom\" -> \"1591__isApprovedOrOwner\"\n\"1591__safeTransfer\" -> \"1591__checkOnERC721Received\"\n\"1591__approve\" -> \"1591_ownerOf\"\n\"1591__safeMint\" -> \"1591__mint\"\n\"1591__isApprovedOrOwner\" -> \"1591_getApproved\"\n\"1591_safeTransferFrom\" -> \"1591__msgSender\"\n\"1591__transfer\" -> \"1591_ownerOf\"\n\"1591__checkOnERC721Received\" -> \"1591__msgSender\"\n\"1591_approve\" -> \"1591_ownerOf\"\n\"1591_transferFrom\" -> \"1591__msgSender\"\n\"1591_safeTransferFrom\" -> \"1591_safeTransferFrom\"\n\"1591__safeMint\" -> \"1591__checkOnERC721Received\"\n\"1591_constructor\" -> \"1591__registerInterface\"\n\"1591__transfer\" -> \"1591__approve\"\n\"1591_getApproved\" -> \"1591__exists\"\n\"1591__burn\" -> \"1591__approve\"\n\"1591_safeTransferFrom\" -> \"1591__safeTransfer\"\n\"1591__setTokenURI\" -> \"1591__exists\"\n\"1591_tokenURI\" -> \"1591_baseURI\"\n\"1591_approve\" -> \"1591__msgSender\"\n\"1591_setApprovalForAll\" -> \"1591__msgSender\"\n\"1591__isApprovedOrOwner\" -> \"1591_ownerOf\"\n\"1591_approve\" -> \"1591__approve\"\n\"1591__mint\" -> \"1591__beforeTokenTransfer\"\n\"1591__burn\" -> \"1591__beforeTokenTransfer\"\n\"1591__safeMint\" -> \"1591__safeMint\"\n\"1591__transfer\" -> \"1591__beforeTokenTransfer\"\n\"1591__mint\" -> \"1591__exists\"\n\"1591_safeTransferFrom\" -> \"1591__isApprovedOrOwner\"\n\"1591__burn\" -> \"1591_ownerOf\"\n\"1591_tokenURI\" -> \"1591__exists\"\n\"1591_approve\" -> \"1591_isApprovedForAll\"\n\"1591_transferFrom\" -> \"1591__transfer\"\n\"1591__isApprovedOrOwner\" -> \"1591_isApprovedForAll\"\n\"1591__safeTransfer\" -> \"1591__transfer\"\n}subgraph cluster_281_ERC165 {\nlabel = \"ERC165\"\n\"281_supportsInterface\" [label=\"supportsInterface\"]\n\"281_constructor\" [label=\"constructor\"]\n\"281__registerInterface\" [label=\"_registerInterface\"]\n\"281_constructor\" -> \"281__registerInterface\"\n}subgraph cluster_solidity {\nlabel = \"[Solidity]\"\n\"abi.decode()\" \n\"balance(address)\" \n\"abi.encodeWithSelector()\" \n\"abi.encodePacked()\" \n\"require(bool,string)\" \n\"keccak256(bytes)\" \n\"1591__checkOnERC721Received\" -> \"abi.encodeWithSelector()\"\n\"3895_withdrawFees\" -> \"require(bool,string)\"\n\"1591_tokenURI\" -> \"abi.encodePacked()\"\n\"1591__mint\" -> \"require(bool,string)\"\n\"1591_transferFrom\" -> \"require(bool,string)\"\n\"3895_refund\" -> \"require(bool,string)\"\n\"281__registerInterface\" -> \"require(bool,string)\"\n\"1591_getApproved\" -> \"require(bool,string)\"\n\"1591_safeTransferFrom\" -> \"require(bool,string)\"\n\"1591__safeTransfer\" -> \"require(bool,string)\"\n\"3895_withdrawFees\" -> \"balance(address)\"\n\"224_transferOwnership\" -> \"require(bool,string)\"\n\"3895_selectWinner\" -> \"keccak256(bytes)\"\n\"3895_selectWinner\" -> \"abi.encodePacked()\"\n\"3895_selectWinner\" -> \"require(bool,string)\"\n\"1591_tokenURI\" -> \"require(bool,string)\"\n\"1591__transfer\" -> \"require(bool,string)\"\n\"1591_setApprovalForAll\" -> \"require(bool,string)\"\n\"1591_approve\" -> \"require(bool,string)\"\n\"1591__checkOnERC721Received\" -> \"abi.decode()\"\n\"3895_enterRaffle\" -> \"require(bool,string)\"\n\"3895_tokenURI\" -> \"require(bool,string)\"\n\"1591_balanceOf\" -> \"require(bool,string)\"\n\"3895_tokenURI\" -> \"abi.encodePacked()\"\n\"1591__isApprovedOrOwner\" -> \"require(bool,string)\"\n\"1591__setTokenURI\" -> \"require(bool,string)\"\n\"1591__safeMint\" -> \"require(bool,string)\"\n}\n}"}, "source_mapping": {}}], "description": "Call Graph: all_contracts.call-graph.dot\nCall Graph: Base64.call-graph.dot\nCall Graph: SafeMath.call-graph.dot\nCall Graph: IERC721Receiver.call-graph.dot\nCall Graph: Address.call-graph.dot\nCall Graph: EnumerableMap.call-graph.dot\nCall Graph: EnumerableSet.call-graph.dot\nCall Graph: Strings.call-graph.dot\nCall Graph: PuppyRaffle.call-graph.dot\n", "markdown": "Call Graph: all_contracts.call-graph.dot\nCall Graph: Base64.call-graph.dot\nCall Graph: SafeMath.call-graph.dot\nCall Graph: IERC721Receiver.call-graph.dot\nCall Graph: Address.call-graph.dot\nCall Graph: EnumerableMap.call-graph.dot\nCall Graph: EnumerableSet.call-graph.dot\nCall Graph: Strings.call-graph.dot\nCall Graph: PuppyRaffle.call-graph.dot\n", "first_markdown_element": "", "id": "0f1b62fe88a3559b3163fb29c6ea6eb65acad258e31d3f8e2e9982017508a96e", "printer": "call-graph"}]}}