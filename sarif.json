{"$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json", "version": "2.1.0", "runs": [{"tool": {"driver": {"name": "Slither", "informationUri": "https://github.com/crytic/slither", "version": "0.11.3", "rules": [{"id": "0-1-arbitrary-send-eth", "name": "arbitrary-send-eth", "properties": {"precision": "high", "security-severity": "8.0"}, "shortDescription": {"text": "Functions that send <PERSON><PERSON> to arbitrary destinations"}, "help": {"text": "Ensure that an arbitrary user cannot withdraw unauthorized funds."}}, {"id": "0-1-weak-prng", "name": "weak-prng", "properties": {"precision": "high", "security-severity": "8.0"}, "shortDescription": {"text": "Weak PRNG"}, "help": {"text": "Do not use `block.timestamp`, `now` or `blockhash` as a source of randomness"}}, {"id": "1-0-incorrect-equality", "name": "incorrect-equality", "properties": {"precision": "very-high", "security-severity": "4.0"}, "shortDescription": {"text": "Dangerous strict equalities"}, "help": {"text": "Don't use strict equality to determine if an account has enough Ether or tokens."}}, {"id": "1-1-reentrancy-no-eth", "name": "reentrancy-no-eth", "properties": {"precision": "high", "security-severity": "4.0"}, "shortDescription": {"text": "Reentrancy vulnerabilities"}, "help": {"text": "Apply the [`check-effects-interactions` pattern](http://solidity.readthedocs.io/en/v0.4.21/security-considerations.html#re-entrancy)."}}, {"id": "2-1-missing-zero-check", "name": "missing-zero-check", "properties": {"precision": "high", "security-severity": "3.0"}, "shortDescription": {"text": "Missing zero address validation"}, "help": {"text": "Check that the address is not zero."}}, {"id": "2-1-reentrancy-events", "name": "reentrancy-events", "properties": {"precision": "high", "security-severity": "3.0"}, "shortDescription": {"text": "Reentrancy vulnerabilities"}, "help": {"text": "Apply the [`check-effects-interactions` pattern](https://docs.soliditylang.org/en/latest/security-considerations.html#re-entrancy)."}}, {"id": "2-1-timestamp", "name": "timestamp", "properties": {"precision": "high", "security-severity": "3.0"}, "shortDescription": {"text": "Block timestamp"}, "help": {"text": "Avoid relying on `block.timestamp`."}}, {"id": "3-0-<PERSON><PERSON><PERSON>", "name": "pragma", "properties": {"precision": "very-high", "security-severity": "0.0"}, "shortDescription": {"text": "Different pragma directives are used"}, "help": {"text": "Use one Solidity version."}}, {"id": "3-1-dead-code", "name": "dead-code", "properties": {"precision": "high", "security-severity": "0.0"}, "shortDescription": {"text": "Dead-code"}, "help": {"text": "Remove unused functions."}}, {"id": "3-0-solc-version", "name": "solc-version", "properties": {"precision": "very-high", "security-severity": "0.0"}, "shortDescription": {"text": "Incorrect versions of Solidity"}, "help": {"text": "\nDeploy with a recent version of Solidity (at least 0.8.0) with no known severe issues.\n\nUse a simple pragma version that allows any of these versions.\nConsider using the latest version of Solidity for testing."}}, {"id": "3-0-low-level-calls", "name": "low-level-calls", "properties": {"precision": "very-high", "security-severity": "0.0"}, "shortDescription": {"text": "Low-level calls"}, "help": {"text": "Avoid low-level calls. Check the call success. If the call is meant for a contract, check for code existence."}}, {"id": "4-0-cache-array-length", "name": "cache-array-length", "properties": {"precision": "very-high", "security-severity": "0.0"}, "shortDescription": {"text": "Cache array length"}, "help": {"text": "Cache the lengths of storage arrays if they are used and not modified in `for` loops."}}, {"id": "4-0-constable-states", "name": "constable-states", "properties": {"precision": "very-high", "security-severity": "0.0"}, "shortDescription": {"text": "State variables that could be declared constant"}, "help": {"text": "Add the `constant` attribute to state variables that never change."}}, {"id": "4-0-immutable-states", "name": "immutable-states", "properties": {"precision": "very-high", "security-severity": "0.0"}, "shortDescription": {"text": "State variables that could be declared immutable"}, "help": {"text": "Add the `immutable` attribute to state variables that never change or are set only in the constructor."}}]}}, "results": [{"ruleId": "0-1-arbitrary-send-eth", "message": {"text": "PuppyRaffle.withdrawFees() (src/PuppyRaffle.sol#157-163) sends eth to arbitrary user\n\tDangerous calls:\n\t- (success,None) = feeAddress.call{value: feesToWithdraw}() (src/PuppyRaffle.sol#161)\n", "markdown": "[PuppyRaffle.withdrawFees()](src/PuppyRaffle.sol#L157-L163) sends eth to arbitrary user\n\tDangerous calls:\n\t- [(success,None) = feeAddress.call{value: feesToWithdraw}()](src/PuppyRaffle.sol#L161)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 157, "endLine": 163}}}], "partialFingerprints": {"id": "d9e15296caa74aa977333163073f28aa5dc263274107044668a804c7a06b7f31"}}, {"ruleId": "0-1-weak-prng", "message": {"text": "PuppyRaffle.selectWinner() (src/PuppyRaffle.sol#125-154) uses a weak PRNG: \"winnerIndex = uint256(keccak256(bytes)(abi.encodePacked(msg.sender,block.timestamp,block.difficulty))) % players.length (src/PuppyRaffle.sol#128-129)\" \n", "markdown": "[PuppyRaffle.selectWinner()](src/PuppyRaffle.sol#L125-L154) uses a weak PRNG: \"[winnerIndex = uint256(keccak256(bytes)(abi.encodePacked(msg.sender,block.timestamp,block.difficulty))) % players.length](src/PuppyRaffle.sol#L128-L129)\" \n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 125, "endLine": 154}}}], "partialFingerprints": {"id": "c0e540edb2741d10cad8a00c35e82d6fefdf1d1b7581ce7b971877f3fec40bc0"}}, {"ruleId": "1-0-incorrect-equality", "message": {"text": "PuppyRaffle.withdrawFees() (src/PuppyRaffle.sol#157-163) uses a dangerous strict equality:\n\t- require(bool,string)(address(this).balance == uint256(totalFees),PuppyRaffle: There are currently players active!) (src/PuppyRaffle.sol#158)\n", "markdown": "[PuppyRaffle.withdrawFees()](src/PuppyRaffle.sol#L157-L163) uses a dangerous strict equality:\n\t- [require(bool,string)(address(this).balance == uint256(totalFees),PuppyRaffle: There are currently players active!)](src/PuppyRaffle.sol#L158)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 157, "endLine": 163}}}], "partialFingerprints": {"id": "ee7491c0970c5c31f2ea17d0aadc1d54b0fdd02a460064e482c5dd1bc0eb86ee"}}, {"ruleId": "1-1-reentrancy-no-eth", "message": {"text": "Reentrancy in PuppyRaffle.refund(uint256) (src/PuppyRaffle.sol#96-105):\n\tExternal calls:\n\t- address(msg.sender).sendValue(entranceFee) (src/PuppyRaffle.sol#101)\n\tState variables written after the call(s):\n\t- players[playerIndex] = address(0) (src/PuppyRaffle.sol#103)\n\tPuppyRaffle.players (src/PuppyRaffle.sol#23) can be used in cross function reentrancies:\n\t- PuppyRaffle.enterRaffle(address[]) (src/PuppyRaffle.sol#79-92)\n\t- PuppyRaffle.getActivePlayerIndex(address) (src/PuppyRaffle.sol#110-117)\n\t- PuppyRaffle.players (src/PuppyRaffle.sol#23)\n\t- PuppyRaffle.refund(uint256) (src/PuppyRaffle.sol#96-105)\n\t- PuppyRaffle.selectWinner() (src/PuppyRaffle.sol#125-154)\n", "markdown": "Reentrancy in [PuppyRaffle.refund(uint256)](src/PuppyRaffle.sol#L96-L105):\n\tExternal calls:\n\t- [address(msg.sender).sendValue(entranceFee)](src/PuppyRaffle.sol#L101)\n\tState variables written after the call(s):\n\t- [players[playerIndex] = address(0)](src/PuppyRaffle.sol#L103)\n\t[PuppyRaffle.players](src/PuppyRaffle.sol#L23) can be used in cross function reentrancies:\n\t- [PuppyRaffle.enterRaffle(address[])](src/PuppyRaffle.sol#L79-L92)\n\t- [PuppyRaffle.getActivePlayerIndex(address)](src/PuppyRaffle.sol#L110-L117)\n\t- [PuppyRaffle.players](src/PuppyRaffle.sol#L23)\n\t- [PuppyRaffle.refund(uint256)](src/PuppyRaffle.sol#L96-L105)\n\t- [PuppyRaffle.selectWinner()](src/PuppyRaffle.sol#L125-L154)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 96, "endLine": 105}}}], "partialFingerprints": {"id": "338d0e4efca2a68838502b6eaff4c8045469282930581d0e866cf2ef0822ebdb"}}, {"ruleId": "2-1-missing-zero-check", "message": {"text": "PuppyRaffle.changeFeeAddress(address).newFeeAddress (src/PuppyRaffle.sol#167) lacks a zero-check on :\n\t\t- feeAddress = newFeeAddress (src/PuppyRaffle.sol#168)\n", "markdown": "[PuppyRaffle.changeFeeAddress(address).newFeeAddress](src/PuppyRaffle.sol#L167) lacks a zero-check on :\n\t\t- [feeAddress = newFeeAddress](src/PuppyRaffle.sol#L168)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 167, "endLine": 167}}}], "partialFingerprints": {"id": "967681cd1911cae196898d4247c076ee14af50b59f231155afedaabea85f1567"}}, {"ruleId": "2-1-missing-zero-check", "message": {"text": "PuppyRaffle.constructor(uint256,address,uint256)._feeAddress (src/PuppyRaffle.sol#60) lacks a zero-check on :\n\t\t- feeAddress = _feeAddress (src/PuppyRaffle.sol#62)\n", "markdown": "[PuppyRaffle.constructor(uint256,address,uint256)._feeAddress](src/PuppyRaffle.sol#L60) lacks a zero-check on :\n\t\t- [feeAddress = _feeAddress](src/PuppyRaffle.sol#L62)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 60, "endLine": 60}}}], "partialFingerprints": {"id": "b97b6562ee8abc7d3d9b6cbb3dfabce99ddd85cc92b9dc1a1b4a329ff6a707f1"}}, {"ruleId": "2-1-reentrancy-events", "message": {"text": "Reentrancy in PuppyRaffle.selectWinner() (src/PuppyRaffle.sol#125-154):\n\tExternal calls:\n\t- (success,None) = winner.call{value: prizePool}() (src/PuppyRaffle.sol#151)\n\t- _safeMint(winner,tokenId) (src/PuppyRaffle.sol#153)\n\t\t- returndata = to.functionCall(abi.encodeWithSelector(IERC721Receiver(to).onERC721Received.selector,_msgSender(),from,tokenId,_data),ERC721: transfer to non ERC721Receiver implementer) (lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol#441-447)\n\t\t- (success,returndata) = target.call{value: value}(data) (lib/openzeppelin-contracts/contracts/utils/Address.sol#119)\n\tExternal calls sending eth:\n\t- (success,None) = winner.call{value: prizePool}() (src/PuppyRaffle.sol#151)\n\t- _safeMint(winner,tokenId) (src/PuppyRaffle.sol#153)\n\t\t- (success,returndata) = target.call{value: value}(data) (lib/openzeppelin-contracts/contracts/utils/Address.sol#119)\n\tEvent emitted after the call(s):\n\t- Transfer(address(0),to,tokenId) (lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol#343)\n\t\t- _safeMint(winner,tokenId) (src/PuppyRaffle.sol#153)\n", "markdown": "Reentrancy in [PuppyRaffle.selectWinner()](src/PuppyRaffle.sol#L125-L154):\n\tExternal calls:\n\t- [(success,None) = winner.call{value: prizePool}()](src/PuppyRaffle.sol#L151)\n\t- [_safeMint(winner,tokenId)](src/PuppyRaffle.sol#L153)\n\t\t- [returndata = to.functionCall(abi.encodeWithSelector(IERC721Receiver(to).onERC721Received.selector,_msgSender(),from,tokenId,_data),ERC721: transfer to non ERC721Receiver implementer)](lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol#L441-L447)\n\t\t- [(success,returndata) = target.call{value: value}(data)](lib/openzeppelin-contracts/contracts/utils/Address.sol#L119)\n\tExternal calls sending eth:\n\t- [(success,None) = winner.call{value: prizePool}()](src/PuppyRaffle.sol#L151)\n\t- [_safeMint(winner,tokenId)](src/PuppyRaffle.sol#L153)\n\t\t- [(success,returndata) = target.call{value: value}(data)](lib/openzeppelin-contracts/contracts/utils/Address.sol#L119)\n\tEvent emitted after the call(s):\n\t- [Transfer(address(0),to,tokenId)](lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol#L343)\n\t\t- [_safeMint(winner,tokenId)](src/PuppyRaffle.sol#L153)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 125, "endLine": 154}}}], "partialFingerprints": {"id": "252bf0dd73da64392a2e52817c7a509358018d1295e7cc4b2ccadf07f786c0ff"}}, {"ruleId": "2-1-reentrancy-events", "message": {"text": "Reentrancy in PuppyRaffle.refund(uint256) (src/PuppyRaffle.sol#96-105):\n\tExternal calls:\n\t- address(msg.sender).sendValue(entranceFee) (src/PuppyRaffle.sol#101)\n\tEvent emitted after the call(s):\n\t- RaffleRefunded(playerAddress) (src/PuppyRaffle.sol#104)\n", "markdown": "Reentrancy in [PuppyRaffle.refund(uint256)](src/PuppyRaffle.sol#L96-L105):\n\tExternal calls:\n\t- [address(msg.sender).sendValue(entranceFee)](src/PuppyRaffle.sol#L101)\n\tEvent emitted after the call(s):\n\t- [RaffleRefunded(playerAddress)](src/PuppyRaffle.sol#L104)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 96, "endLine": 105}}}], "partialFingerprints": {"id": "28dd1188f0771dbc6c74a01aed1699d75fff882010bb3a1f17445255acd785fd"}}, {"ruleId": "2-1-timestamp", "message": {"text": "PuppyRaffle.selectWinner() (src/PuppyRaffle.sol#125-154) uses timestamp for comparisons\n\tDangerous comparisons:\n\t- require(bool,string)(block.timestamp >= raffleStartTime + raffleDuration,PuppyRaffle: Raffle not over) (src/PuppyRaffle.sol#126)\n", "markdown": "[PuppyRaffle.selectWinner()](src/PuppyRaffle.sol#L125-L154) uses timestamp for comparisons\n\tDangerous comparisons:\n\t- [require(bool,string)(block.timestamp >= raffleStartTime + raffleDuration,PuppyRaffle: Raffle not over)](src/PuppyRaffle.sol#L126)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 125, "endLine": 154}}}], "partialFingerprints": {"id": "812c9539fe95bc35182617b254aceae70cc745f1e95b93649f9e18f2fa0c0351"}}, {"ruleId": "3-0-<PERSON><PERSON><PERSON>", "message": {"text": "4 different versions of Solidity are used:\n\t- Version constraint >=0.6.0 is used by:\n\t\t->=0.6.0 (lib/base64/base64.sol#3)\n\t- Version constraint >=0.6.0<0.8.0 is used by:\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/access/Ownable.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/introspection/ERC165.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/introspection/IERC165.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/math/SafeMath.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/utils/Context.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/utils/EnumerableMap.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/utils/EnumerableSet.sol#3)\n\t\t->=0.6.0<0.8.0 (lib/openzeppelin-contracts/contracts/utils/Strings.sol#3)\n\t- Version constraint >=0.6.2<0.8.0 is used by:\n\t\t->=0.6.2<0.8.0 (lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol#3)\n\t\t->=0.6.2<0.8.0 (lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Enumerable.sol#3)\n\t\t->=0.6.2<0.8.0 (lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Metadata.sol#3)\n\t\t->=0.6.2<0.8.0 (lib/openzeppelin-contracts/contracts/utils/Address.sol#3)\n\t- Version constraint ^0.7.6 is used by:\n\t\t-^0.7.6 (src/PuppyRaffle.sol#2)\n", "markdown": "4 different versions of Solidity are used:\n\t- Version constraint >=0.6.0 is used by:\n\t\t-[>=0.6.0](lib/base64/base64.sol#L3)\n\t- Version constraint >=0.6.0<0.8.0 is used by:\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/access/Ownable.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/introspection/ERC165.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/introspection/IERC165.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/math/SafeMath.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/utils/Context.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/utils/EnumerableMap.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/utils/EnumerableSet.sol#L3)\n\t\t-[>=0.6.0<0.8.0](lib/openzeppelin-contracts/contracts/utils/Strings.sol#L3)\n\t- Version constraint >=0.6.2<0.8.0 is used by:\n\t\t-[>=0.6.2<0.8.0](lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol#L3)\n\t\t-[>=0.6.2<0.8.0](lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Enumerable.sol#L3)\n\t\t-[>=0.6.2<0.8.0](lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Metadata.sol#L3)\n\t\t-[>=0.6.2<0.8.0](lib/openzeppelin-contracts/contracts/utils/Address.sol#L3)\n\t- Version constraint ^0.7.6 is used by:\n\t\t-[^0.7.6](src/PuppyRaffle.sol#L2)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "lib/base64/base64.sol"}, "region": {"startLine": 3, "endLine": 3}}}], "partialFingerprints": {"id": "a4b7bc0b460f8803a034cf51625d0b3500d9f0279ecac3deda657b44b93b378c"}}, {"ruleId": "3-1-dead-code", "message": {"text": "PuppyRaffle._isActivePlayer() (src/PuppyRaffle.sol#173-180) is never used and should be removed\n", "markdown": "[PuppyRaffle._isActivePlayer()](src/PuppyRaffle.sol#L173-L180) is never used and should be removed\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 173, "endLine": 180}}}], "partialFingerprints": {"id": "311fa6a66ba7ad6889b3569e81b65348a8806f708433bb087d918de7ae28686a"}}, {"ruleId": "3-0-solc-version", "message": {"text": "Version constraint ^0.7.6 contains known severe issues (https://solidity.readthedocs.io/en/latest/bugs.html)\n\t- FullInlinerNonExpressionSplitArgumentEvaluationOrder\n\t- MissingSideEffectsOnSelectorAccess\n\t- AbiReencodingHeadOverflowWithStaticArrayCleanup\n\t- DirtyBytesArrayToStorage\n\t- DataLocationChangeInInternalOverride\n\t- NestedCalldataArrayAbiReencodingSizeValidation\n\t- SignedImmutables\n\t- ABIDecodeTwoDimensionalArrayMemory\n\t- KeccakCaching.\nIt is used by:\n\t- ^0.7.6 (src/PuppyRaffle.sol#2)\n", "markdown": "Version constraint ^0.7.6 contains known severe issues (https://solidity.readthedocs.io/en/latest/bugs.html)\n\t- FullInlinerNonExpressionSplitArgumentEvaluationOrder\n\t- MissingSideEffectsOnSelectorAccess\n\t- AbiReencodingHeadOverflowWithStaticArrayCleanup\n\t- DirtyBytesArrayToStorage\n\t- DataLocationChangeInInternalOverride\n\t- NestedCalldataArrayAbiReencodingSizeValidation\n\t- SignedImmutables\n\t- ABIDecodeTwoDimensionalArrayMemory\n\t- KeccakCaching.\nIt is used by:\n\t- [^0.7.6](src/PuppyRaffle.sol#L2)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 2, "endLine": 2}}}], "partialFingerprints": {"id": "9e101dddf274cc17a3057f71452336ddffc0fc03d2dada4176f959354f43640c"}}, {"ruleId": "3-0-low-level-calls", "message": {"text": "Low level call in PuppyRaffle.selectWinner() (src/PuppyRaffle.sol#125-154):\n\t- (success,None) = winner.call{value: prizePool}() (src/PuppyRaffle.sol#151)\n", "markdown": "Low level call in [PuppyRaffle.selectWinner()](src/PuppyRaffle.sol#L125-L154):\n\t- [(success,None) = winner.call{value: prizePool}()](src/PuppyRaffle.sol#L151)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 125, "endLine": 154}}}], "partialFingerprints": {"id": "3ca4ae3610c9d614da30cab805d8c7feb41a727363d5d27a2efe433e6738764f"}}, {"ruleId": "3-0-low-level-calls", "message": {"text": "Low level call in PuppyRaffle.withdrawFees() (src/PuppyRaffle.sol#157-163):\n\t- (success,None) = feeAddress.call{value: feesToWithdraw}() (src/PuppyRaffle.sol#161)\n", "markdown": "Low level call in [PuppyRaffle.withdrawFees()](src/PuppyRaffle.sol#L157-L163):\n\t- [(success,None) = feeAddress.call{value: feesToWithdraw}()](src/PuppyRaffle.sol#L161)\n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 157, "endLine": 163}}}], "partialFingerprints": {"id": "60d96ba39d63035f26d0ef5c6eabce2640044b80c454c35cb1fe87d02f6b84b9"}}, {"ruleId": "4-0-cache-array-length", "message": {"text": "Loop condition i < players.length (src/PuppyRaffle.sol#111) should use cached array length instead of referencing `length` member of the storage array.\n ", "markdown": "Loop condition [i < players.length](src/PuppyRaffle.sol#L111) should use cached array length instead of referencing `length` member of the storage array.\n "}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 111, "endLine": 111}}}], "partialFingerprints": {"id": "436de13fb53dec30e5ca83e1c06c1ce6a3cf18e61b786d9f69db1fd82c3b8324"}}, {"ruleId": "4-0-cache-array-length", "message": {"text": "Loop condition i < players.length (src/PuppyRaffle.sol#174) should use cached array length instead of referencing `length` member of the storage array.\n ", "markdown": "Loop condition [i < players.length](src/PuppyRaffle.sol#L174) should use cached array length instead of referencing `length` member of the storage array.\n "}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 174, "endLine": 174}}}], "partialFingerprints": {"id": "4eb72f936f740e957ddf18f4df3922169699df4830e53a68da249e24980d1d01"}}, {"ruleId": "4-0-cache-array-length", "message": {"text": "Loop condition j < players.length (src/PuppyRaffle.sol#87) should use cached array length instead of referencing `length` member of the storage array.\n ", "markdown": "Loop condition [j < players.length](src/PuppyRaffle.sol#L87) should use cached array length instead of referencing `length` member of the storage array.\n "}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 87, "endLine": 87}}}], "partialFingerprints": {"id": "dfc8316b52acdb9c03b55c05b30c00a877aab0c8616fb2a64c4258ca5a560b8a"}}, {"ruleId": "4-0-constable-states", "message": {"text": "PuppyRaffle.commonImageUri (src/PuppyRaffle.sol#38) should be constant \n", "markdown": "[PuppyRaffle.commonImageUri](src/PuppyRaffle.sol#L38) should be constant \n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 38, "endLine": 38}}}], "partialFingerprints": {"id": "583b895e738937d173ada979cd09698c9171da1b1bf9a277f4de10fd1fba5798"}}, {"ruleId": "4-0-constable-states", "message": {"text": "PuppyRaffle.legendaryImageUri (src/PuppyRaffle.sol#48) should be constant \n", "markdown": "[PuppyRaffle.legendaryImageUri](src/PuppyRaffle.sol#L48) should be constant \n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 48, "endLine": 48}}}], "partialFingerprints": {"id": "c9a95b33030bb5803885604726bc63ecb53e91b4309d92aa5e22c76f82176684"}}, {"ruleId": "4-0-constable-states", "message": {"text": "PuppyRaffle.rareImageUri (src/PuppyRaffle.sol#43) should be constant \n", "markdown": "[PuppyRaffle.rareImageUri](src/PuppyRaffle.sol#L43) should be constant \n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 43, "endLine": 43}}}], "partialFingerprints": {"id": "f1fc6b30db9cd57b33f9a62059d50a4d7f5cc49aa1c136817086767b6ca9b548"}}, {"ruleId": "4-0-immutable-states", "message": {"text": "PuppyRaffle.raffleDuration (src/PuppyRaffle.sol#24) should be immutable \n", "markdown": "[PuppyRaffle.raffleDuration](src/PuppyRaffle.sol#L24) should be immutable \n"}, "level": "warning", "locations": [{"physicalLocation": {"artifactLocation": {"uri": "src/PuppyRaffle.sol"}, "region": {"startLine": 24, "endLine": 24}}}], "partialFingerprints": {"id": "2ae749432aeb6928e3650cf4190df23b3ff889b4b31fdc3eeccd8ecc43b5f167"}}]}]}