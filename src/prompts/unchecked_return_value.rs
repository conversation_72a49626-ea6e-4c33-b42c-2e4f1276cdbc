pub const UNCHECK_RETURN_VALUES: &str = r#"

Your ONLY goal is to detect **external calls whose boolean success
return value is NOT verified or bubbled up.**

──────────────────────────────────
⚠️  VALID–BUG CRITERIA
──────────────────────────────────
A finding is reportable **ONLY if _all_ of the following hold**:

1. **Ignored Success Flag**  
   * For `.call{…}()`, `.delegatecall()`, `.staticcall()`, or
     `.send()` the returned `(bool success, …)` (or single `bool` for
     `send`) is **not**:
     - used in `require(success, …)`  
     - wrapped in `if (!success) revert …;`  
     - returned to the caller, **or**  
     - handled by a library that already reverts on failure  
       (e.g. `Address.sendValue`, `SafeERC20.safeTransfer`).
2. **Interface Calls**  
   The function returns `bool` (e.g., `ERC20.transfer`) and that value
   is ignored **and** no SafeERC20/try-catch wrapper is present.
3. **No CEI Commentary**  
   Do **NOT** flag state-update-before-call ordering; if the success
   flag *is* checked, the call is **out of scope** for this audit.

──────────────────────────────────
OUTPUT FORMAT  
──────────────────────────────────
"#;
