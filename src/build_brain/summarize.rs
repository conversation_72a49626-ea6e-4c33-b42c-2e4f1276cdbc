use anyhow::Result;
use log::info;
use once_cell::sync::Lazy;
use rig::{
    client::{CompletionClient, ProviderClient},
    providers::openai::{self, GPT_4O, O3},
};
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::Mutex;
use walkdir::WalkDir;

use crate::{
    cost::cost_data::add_to_inference_cost_by_type, prepare_code::git_clone::RepoPaths,
    utils::extract_retry::extractor_with_retry,
};
use crate::{
    cost::cost_data::LlmCostType,
    llm_review::prompt_content::{self, generate_context_for_code_review},
};

use super::slither_ffi::cache_key;

/// Global cache keyed by (repo_root, printer) tuple stringified
pub static FILE_SUMMARY_CACHE: Lazy<Arc<Mutex<HashMap<String, Vec<SrcFileSummary>>>>> =
    Lazy::new(|| Arc::new(Mutex::new(HashMap::new())));
#[derive(Default, Debug, Clone)]
pub struct SrcFileSummary {
    pub filename: String,
    pub summary: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct FileSummary {
    pub summary: String,
}

pub async fn summarize_src_files(
    repo: &RepoPaths,
    semantics_path: &Path,
) -> Result<Vec<SrcFileSummary>> {
    let key = cache_key(&repo.root, "file_summaries");
    let cache = Arc::clone(&FILE_SUMMARY_CACHE);
    let mut summaries_cache = cache.lock().await;

    // Return cached output if exists
    if let Some(cached) = summaries_cache.get(&key) {
        return Ok(cached.clone());
    }

    // Initialize vectors to store file paths
    let mut summaries = Vec::<SrcFileSummary>::new();

    let openai_client = openai::Client::from_env();

    let context =
        prompt_content::generate_slither_metadata_prompt_context(repo, &semantics_path).await?;

    // info!("slither metadata => {:#?}", context);
    info!("generate summmary of all major files and docs in repo...");
    let preamble ="You are a senior solidity dev. Please summarize below content (code or docs). Format in markdown for easy reading. 
                    If content is code. Please write 200 word or less summary for each contract plus contract definition, 100 words or less summary 
                    of each function + function interface, and 50 word or less explanation of each storage variable + variable defintion. If docs 
                    please summarize each section of the docs with 150 words or less, max 500 words total for each doc file. 
                    Respond only with valid JSON matching the schema!";
    let ai_summary_agent = openai_client
        .extractor::<FileSummary>(GPT_4O)
        .preamble(preamble)
        .context(&context)
        .build();

    // Walk through the repository and collect relevant files
    let repo_code_root = repo.root.join(repo.repo_name.clone());
    for entry in WalkDir::new(&repo_code_root)
        .into_iter()
        .filter_map(Result::ok)
    {
        let path = entry.path();

        // Skip directories and symlinks
        if !path.is_file() || fs::symlink_metadata(path)?.file_type().is_symlink() {
            continue;
        }

        let is_readme = path
            .file_name()
            .map(|f| f.to_ascii_lowercase() == "readme.md")
            .unwrap_or(false)
            && (path.parent() == Some(&repo_code_root)
                || path.parent() == Some(&repo_code_root.join("src")));

        let is_sol_in_src = path.extension().map_or(false, |ext| ext == "sol")
            && path.starts_with(&repo_code_root.join("src"));

        if is_readme || is_sol_in_src {
            let content = fs::read_to_string(path)?;

            add_to_inference_cost_by_type(
                &format!("{}{}", preamble, content),
                LlmCostType::Openai4oInput,
            )
            .await;

            let summary =
                extractor_with_retry(&ai_summary_agent, &content, LlmCostType::Openai4oOutput)
                    .await?;

            // filename is relative to root folder ie src/PuppyRaffle.sol
            let file = path.strip_prefix(&repo.root)?.to_string_lossy().to_string();

            summaries.push(SrcFileSummary {
                filename: file,
                summary: summary.summary,
            })
        }
    }

    log::info!("summaries => {:#?}", summaries);

    summaries_cache.insert(key, summaries.clone());
    Ok(summaries)
}

pub async fn summarize_protocol(repo: &RepoPaths, semantics_path: &Path) -> Result<String> {
    // let key = cache_key(repo_root, "protocol-summary");
    // let cache = Arc::clone(&FILE_SUMMARY_CACHE);
    // let mut summaries_cache = cache.lock().await;
    //
    // // Return cached output if exists
    // if let Some(cached) = summaries_cache.get(&key) {
    //     let summary = cached
    //         .first()
    //         .unwrap_or(&SrcFileSummary::default())
    //         .summary
    //         .clone();
    //     return Ok(summary);
    // }

    // Initialize vectors to store file paths
    let mut summaries = Vec::<SrcFileSummary>::new();

    let openai_client = openai::Client::from_env();

    log::info!("generate context for code review");
    let content = generate_context_for_code_review(repo, &semantics_path).await?;
    let preamble= "You are a senior solidity dev. Given the context provided for solidity smart contract protocol, 
                   please create a max 200 word summary of this protocol explaining what it is, and how it works.  Format 
                   in markdown for easy reading. Respond only with valid JSON matching the schema!";

    let ai_summary_agent = openai_client
        .extractor::<FileSummary>(O3)
        .preamble(preamble)
        .build();

    log::info!("extracting protocol summary");
    // rerun if NoDataExtracted Error

    add_to_inference_cost_by_type(
        &format!("{}{}", preamble, content),
        LlmCostType::OpenaiO3Output,
    )
    .await;

    let summary =
        extractor_with_retry(&ai_summary_agent, &content, LlmCostType::OpenaiO3Output).await?;

    log::info!("protocol summary => {:#?}", summary);

    summaries.push(SrcFileSummary {
        filename: "protocol-summary".to_string(),
        summary: summary.summary.clone(),
    });

    // summaries_cache.insert(key, summaries.clone());
    Ok(summary.summary)
}
